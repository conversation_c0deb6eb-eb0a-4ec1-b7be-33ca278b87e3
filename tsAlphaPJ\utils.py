from sqlalchemy import inspect, text
import pandas as pd
import logging
import CONF  # 引用配置文件
from sqlalchemy.exc import SQLAlchemyError

# 配置日志记录，指定编码为 utf-8
logging.basicConfig(**CONF.LOGGING_CONFIG)

def save_to_mysql(engine, table_name, df, unique_keys):
    temp_table_name = f"temp_{table_name}"
    try:
        with engine.connect() as conn:
            # 检查表是否存在，如果不存在则创建表
            if not inspect(engine).has_table(table_name):
                df.head(0).to_sql(table_name, con=conn, index=False, if_exists='replace')
                logging.info(f"表 {table_name} 不存在，创建表")

            # 将数据写入临时表
            # 确保临时表不存在，如果存在则删除
            try:
                conn.execute(text(f"DROP TABLE IF EXISTS `{temp_table_name}`;"))
            except Exception:
                # 忽略删除失败的错误，可能表本来就不存在
                pass

            df.to_sql(temp_table_name, con=conn, index=False, if_exists='replace')

            # 使用 INSERT IGNORE 插入数据
            columns = ', '.join([f"`{col}`" for col in df.columns])
            insert_sql = text(f"""
                INSERT IGNORE INTO `{table_name}` ({columns})
                SELECT {columns} FROM `{temp_table_name}`;
            """)
            conn.execute(insert_sql)

            # 删除临时表
            conn.execute(text(f"DROP TABLE IF EXISTS `{temp_table_name}`;"))
            #logging.info(f"插入数据到 {table_name}")
    except Exception as e:
        logging.error(f"保存到 {table_name} 时发生错误: {e}")
        # 确保在出错时也清理临时表
        try:
            with engine.connect() as conn:
                conn.execute(text(f"DROP TABLE IF EXISTS `{temp_table_name}`;"))
        except Exception:
            # 忽略清理失败的错误
            pass

def check_db_connection(engine):
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        logging.info("数据库连接成功")
    except Exception as e:
        logging.error(f"数据库连接失败: {e}")

def table_exists(engine, table_name):
    inspector = inspect(engine)
    return inspector.has_table(table_name)

def validate_dataframe(df, required_columns=None):
    if df.empty:
        raise ValueError("数据框为空")
    if required_columns:
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"数据框缺少必要的列: {missing_columns}")

def log_message(message):
    logging.info(message)

def filter_existing_codes(engine, table_name, ts_codes):
    """批量检查本地数据库中已存在的股票数据，并过滤出需要下载的股票代码"""
    log_message(f"检查本地数据库中已存在的股票数据")
    query = f"SELECT DISTINCT ts_code FROM {table_name}"
    df_existing = pd.read_sql(query, engine)
    existing_ts_codes = df_existing['ts_code'].tolist()
    log_message(f"本地数据库中已存在 {len(existing_ts_codes)} 个股票代码")
    
    ts_codes_to_download = [ts_code for ts_code in ts_codes if ts_code not in existing_ts_codes]
    log_message(f"需要下载的股票代码数量: {len(ts_codes_to_download)}")
    
    return ts_codes_to_download

#stock_daily,stock_weekly,stock_monthly方法可以获取到的字段为：ts_code,trade_date,open,high,low,close,pre_close,change,pct_chg,vol,amount
def get_stock_data(engine, ts_code, start='20200101', end='20211231', freq='daily'):
    """从本地数据库获取数据并转换为Backtrader格式"""
    if freq not in ['daily', 'weekly', 'monthly']:
        raise ValueError("无效的频率。必须是 'daily', 'weekly', 'monthly' 之一。")

    table_name = f'stock_{freq}'
    query = f"""
        SELECT * FROM {table_name}
        WHERE ts_code = '{ts_code}' AND trade_date BETWEEN '{start}' AND '{end}'
        ORDER BY trade_date
    """
    df = pd.read_sql(query, engine)

    if df is not None and isinstance(df, pd.DataFrame):
        df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d')
        df = df.sort_values('trade_date').set_index('trade_date')
        return df[['ts_code', 'open', 'high', 'low', 'close', 'pre_close', 'change', 'pct_chg', 'vol', 'amount']]
    else:
        raise ValueError(f"没有获取到股票 {ts_code} 的数据")